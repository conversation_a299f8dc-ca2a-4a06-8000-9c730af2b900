package com.seu.config;


import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.mapper.MapperScannerConfigurer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Configuration;



import javax.sql.DataSource;

@Configuration
public class MybatisConfig {

    //第一个bean，用来创建SqlSessionFactoryBean，到时候可以直接注入
    //这个输入Datasorce根据类型自动装配的
    @Bean
    public SqlSessionFactoryBean getSqlSessionFactoryBean(DataSource dataSource) {
        SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(dataSource);//配置数据源，自动装配的
        sqlSessionFactoryBean.setTypeAliasesPackage("com.seu.pojo");
        return sqlSessionFactoryBean;
    }
    //第二个bean，作用是进行扫描映射，绑定dao包。因为dao包里面的是接口，不能直接创建对象，需要自动注入
    @Bean
    public MapperScannerConfigurer getMapperScannerConfigurer() {
        MapperScannerConfigurer mapperScannerConfigurer = new MapperScannerConfigurer();
        mapperScannerConfigurer.setBasePackage("com.seu.dao");
        return mapperScannerConfigurer;
    }
}
