package com.seu.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
//在result类中，规范化返回的结果。
//
public class Result {
    private Object data;//封装的数据
    private String message;//成功和失败返回的信息
    private int code;//代表是否成功的代码，0代表失败，1代表成功

    public static Result success(Object data) {
        System.out.println("---------------------------------");
        return new Result(data,"操作成功！",1);

    }

    public static Result error(Object data){
        System.out.println("---------------------------------");
        return new Result(data,"操作失败！",0);
    }
}

