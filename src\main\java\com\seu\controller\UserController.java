package com.seu.controller;

import com.seu.pojo.Result;
import com.seu.pojo.User;
import com.seu.service.UserService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

//添加功能：增删改查
@Controller
@RequestMapping("/user123")
public class UserController {
    @Autowired
    private UserService userService;

    @ResponseBody
    @PostMapping
    public Result addUser(@RequestBody User user) {
        return Result.success(userService.addUser(user));
    }


    @ResponseBody
    @DeleteMapping("/{id}")
    public Result deleteUser(@PathVariable("id") int id) {
        return Result.success(userService.deleteUser(id));
    }


    @ResponseBody
    @PostMapping("/update")
    public Result updateUser(@RequestBody User user) {
        return Result.success(userService.updateUser(user));
    }


    @GetMapping("/{id}")
    @ResponseBody
    public Result getUser(@PathVariable("id") int id) {
        return Result.success(userService.getUser(id));
    }


    @ResponseBody
    @GetMapping
    public Result getAllUsers() {
        return Result.success(userService.getAllUsers());
    }

}
