package com.seu.dao;

import com.seu.pojo.User;
import org.apache.ibatis.annotations.*;
import java.util.List;


public interface UserDao {

    //增加，POST:"/user?......"，返回void
    @Insert("insert into db01.user (name, money) values (#{name},#{money})")
    void addUser(User user);

    //查询所有，GET:"/user"
    @Select("select * from db01.user")
    List<User> getAllUsers();

    //查询单个，GET"/user/id"
    @Select("select name,money from db01.user where id = #{id}")
    User getUser(int id);

    //删除，DELETE:"/user"
    @Delete("delete from db01.user where id = #{id}")
    void deleteUser(int id);

    //更新
    @Update("update db01.user set name = #{name} , money = #{money} where id = #{id}")
    void updateUser(User user);

}
