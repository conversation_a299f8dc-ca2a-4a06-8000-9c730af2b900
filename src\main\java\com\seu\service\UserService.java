package com.seu.service;

import com.seu.dao.UserDao;
import com.seu.pojo.User;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public interface UserService {


    String addUser(User user);

    List<User> getAllUsers();

    User getUser(int id);

    String deleteUser(int id);

    String updateUser(User user);
}
