package com.seu.config;


import org.springframework.web.servlet.support.AbstractAnnotationConfigDispatcherServletInitializer;

public class ServletConfig extends AbstractAnnotationConfigDispatcherServletInitializer {

    @Override
    //指定spring核心容器的配置类
    protected Class<?>[] getRootConfigClasses() {
        return new Class[]{SpringConfig.class};
    }

    @Override
/*  用于加载Spring MVC 容器的配置类，这些配置仅针对Web 层，比如：
    控制器（Controller）的扫描和配置；
    视图解析器（ViewResolver）、拦截器（Interceptor）的配置；
    消息转换器（MessageConverter）、静态资源处理等 Web 相关配置。*/
    protected Class<?>[] getServletConfigClasses() {
        return new Class[]{SpringMvcConfig.class};
    }

    @Override
    //设置mvc处理的请求参数，这边是设置所有请求参数都交给mvc处理
    protected String[] getServletMappings() {
        return new String[]{"/"};
    }
}
