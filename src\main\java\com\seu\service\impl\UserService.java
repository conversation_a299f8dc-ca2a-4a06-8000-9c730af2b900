package com.seu.service.impl;

import com.seu.dao.UserDao;
import com.seu.pojo.User;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UserService implements com.seu.service.UserService {

    @Autowired
    UserDao userDao;

    @Override
    public String addUser(User user) {
        System.out.println("成功调用service层方法");
        userDao.addUser(user);
        return "成功添加user，添加的内容如下：" + user.toString();
    }

    @Override
    public List<User> getAllUsers() {
        System.out.println("成功调用service层方法");
        return userDao.getAllUsers();
    }

    @Override
    public User getUser(int id) {
        System.out.println("成功调用service层方法");
        return userDao.getUser(id);
    }

    @Override
    public String deleteUser(int id) {
        System.out.println("成功调用service层方法");
        userDao.deleteUser(id);
        return "成功删除user，删除的id是:" + id;
    }

    @Override
    public String updateUser(User user) {
        System.out.println("成功调用service层方法");
        userDao.updateUser(user);
        return "成功更新用户信息，更新的信息：" + user.toString();
    }
}
