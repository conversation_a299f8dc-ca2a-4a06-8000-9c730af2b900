package com.seu.config;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.PropertySource;


//整合配置，包括数据源datasource，数据库信息，servlet信息等
@Configuration
@ComponentScan({"com.seu.service"})
@PropertySource("classpath:jdbc.properties")
@Import({MybatisConfig.class,JdbcConfig.class})
public class SpringConfig {
}
